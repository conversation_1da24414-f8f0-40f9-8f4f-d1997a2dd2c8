#include <Arduino.h>
#include <HardwareSerial.h>
#include "sbus.h"

// SBUS差速小车控制程序
// 输入：飞控SBUS信号中的Channel1(左油门)和Channel3(右油门)
// 输出：差速转弯控制信号 - 通道1(转弯)，通道3(前进后退)

// SBUS信号范围
const int SBUS_MIN = 172;
const int SBUS_MAX = 1811;
const int SBUS_MID = (SBUS_MAX - SBUS_MIN) / 2 + SBUS_MIN;

// 输出SBUS范围（保持原有范围）
const int OUTPUT_MIN = 240;
const int OUTPUT_MAX = 1808;
const int OUTPUT_MID = 1024;

// 死区设置
const int DEAD_ZONE = 10; // 中位死区

// 限幅函数
int valueClamp(int value, int minValue, int maxValue)
{
    if (value < minValue)
        return minValue;
    if (value > maxValue)
        return maxValue;
    return value;
}

// 死区处理函数
int applyDeadZone(int value, int center, int deadZone)
{
    if (abs(value - center) < deadZone)
    {
        return center;
    }
    return value;
}

// 差速控制算法
// 输入：左油门(throttle_left)，右油门(throttle_right)
// 输出：转弯控制(turn_output)，前进后退控制(throttle_output)
void differentialToTankControl(int throttle_left, int throttle_right, int &turn_output, int &throttle_output)
{
    // 将SBUS值转换为1000到2000的范围
    int left_normalized = map(throttle_left, SBUS_MIN, SBUS_MAX, 1000, 2000);
    int right_normalized = map(throttle_right, SBUS_MIN, SBUS_MAX, 1000, 2000);

    // 应用死区
    left_normalized = applyDeadZone(left_normalized, 1500, DEAD_ZONE);
    right_normalized = applyDeadZone(right_normalized, 1500, DEAD_ZONE);

    // 差速控制算法
    // 前进后退 = (左 + 右) / 2
    // 转弯 = (右 - 左) / 2
    int throttle_calc = (left_normalized + right_normalized) / 2;
    int turn_calc = (right_normalized - left_normalized) / 2;

    // 转换为SBUS输出范围
    throttle_output = map(throttle_calc, 1000, 2000, OUTPUT_MIN, OUTPUT_MAX);
    turn_output = map(turn_calc, 1000, 2000, OUTPUT_MIN, OUTPUT_MAX);

    // 限幅
    throttle_output = valueClamp(throttle_output, OUTPUT_MIN, OUTPUT_MAX);
    turn_output = valueClamp(turn_output, OUTPUT_MIN, OUTPUT_MAX);
}

typedef struct
{
    int8_t sbus_rx_pin;
    int8_t sbus_tx_pin;
    int8_t led_pin;
} PinDefinition;

// 结构体实例化 - 独立的SBUS引脚
PinDefinition pin_definition = {
    .sbus_rx_pin = 20, // SBUS专用接收引脚
    .sbus_tx_pin = 21, // SBUS专用发送引脚
    .led_pin = 8       // 状态指示LED
};

HardwareSerial SerialPrint(0); // USB串口用于调试输出
HardwareSerial SerialSbus(1);  // 硬件串口1用于SBUS输入和输出

bfs::SbusRx sbus_rx(&SerialSbus, pin_definition.sbus_rx_pin, pin_definition.sbus_tx_pin, true);
bfs::SbusTx sbus_tx(&SerialSbus, pin_definition.sbus_rx_pin, pin_definition.sbus_tx_pin, true);
bfs::SbusData sbus_data;
bfs::SbusData sbus_tx_data; // 用于发送的SBUS数据

void setup()
{
    // 初始化USB串口用于调试输出
    SerialPrint.begin(115200);
    pinMode(pin_definition.led_pin, OUTPUT);

    // 初始化SBUS通信
    SerialSbus.Begin();
    // sbus_tx.Begin();

    // 初始化输出数据为中位值
    for (int i = 0; i < 16; i++)
    {
        sbus_tx_data.ch[i] = OUTPUT_MID;
    }
    sbus_tx_data.lost_frame = false;
    sbus_tx_data.failsafe = false;

    SerialPrint.println("=== SBUS差速小车控制器 ===");
    SerialPrint.println("读取飞控SBUS信号并转换为差速控制...");
    SerialPrint.println("引脚配置:");
    SerialPrint.print("  SBUS RX: GPIO");
    SerialPrint.println(pin_definition.sbus_rx_pin);
    SerialPrint.print("  SBUS TX: GPIO");
    SerialPrint.println(pin_definition.sbus_tx_pin);
    SerialPrint.print("  LED: GPIO");
    SerialPrint.println(pin_definition.led_pin);
    SerialPrint.println("控制映射:");
    SerialPrint.println("  输入: CH1(左油门) + CH3(右油门)");
    SerialPrint.println("  输出: CH1(转弯控制) + CH3(前进后退控制)");
    SerialPrint.println("  输出范围: 240~1808, 中位值: 1024");
    SerialPrint.println("============================");
}

void loop()
{
    static unsigned long lastPrint = 0;
    static bool ledState = false;
    static int turn_output = OUTPUT_MID;
    static int throttle_output = OUTPUT_MID;

    // 读取SBUS信号
    if (sbus_rx.Read())
    {
        sbus_data = sbus_rx.data();

        // 获取Channel1(左油门)和Channel3(右油门)
        int throttle_left = sbus_data.ch[0];  // Channel 1
        int throttle_right = sbus_data.ch[2]; // Channel 3

        // 差速控制转换
        differentialToTankControl(throttle_left, throttle_right, turn_output, throttle_output);

        // 设置输出数据
        // 其他通道保持输入值并转换到输出范围
        for (int i = 0; i < 16; i++)
        {
            if (i != 0 && i != 2) // 除了Channel1和Channel3
            {
                // 将输入SBUS范围转换到输出范围
                sbus_tx_data.ch[i] = map(sbus_data.ch[i], SBUS_MIN, SBUS_MAX, OUTPUT_MIN, OUTPUT_MAX);
                sbus_tx_data.ch[i] = valueClamp(sbus_tx_data.ch[i], OUTPUT_MIN, OUTPUT_MAX);
            }
        }

        // 设置转换后的控制通道
        sbus_tx_data.ch[0] = turn_output;     // Channel 1: 转弯控制
        sbus_tx_data.ch[2] = throttle_output; // Channel 3: 前进后退控制

        // 复制失联和故障安全状态
        sbus_tx_data.lost_frame = sbus_data.lost_frame;
        sbus_tx_data.failsafe = sbus_data.failsafe;

        // 设置转换后的数据并发送
        sbus_tx.data(sbus_tx_data);
        sbus_tx.Write();

        // LED指示数据接收和发送
        ledState = !ledState;
        digitalWrite(pin_definition.led_pin, ledState);
    }

    // 每200ms打印一次调试信息
    if (millis() - lastPrint >= 200)
    {
        lastPrint = millis();

        SerialPrint.print("输入 - 左油门:");
        SerialPrint.print(sbus_data.ch[0]);
        SerialPrint.print(" 右油门:");
        SerialPrint.print(sbus_data.ch[2]);

        SerialPrint.print(" | 输出 - 转弯:");
        SerialPrint.print(turn_output);
        SerialPrint.print(" 前进后退:");
        SerialPrint.print(throttle_output);

        // 显示其他几个重要通道的转换情况
        SerialPrint.print(" | 其他通道 - CH2:");
        SerialPrint.print(sbus_data.ch[1]);
        SerialPrint.print("->");
        SerialPrint.print(sbus_tx_data.ch[1]);

        SerialPrint.print(" | 状态 - 失联:");
        SerialPrint.print(sbus_data.lost_frame ? "是" : "否");
        SerialPrint.print(" 故障安全:");
        SerialPrint.print(sbus_data.failsafe ? "是" : "否");
        SerialPrint.println();
    }

    // 故障安全处理
    if (sbus_data.lost_frame || sbus_data.failsafe)
    {
        // 失联或故障安全时，Channel1和Channel3输出中位值停止小车
        // 其他通道保持最后的有效值
        sbus_tx_data.ch[0] = OUTPUT_MID; // 转弯控制停止
        sbus_tx_data.ch[2] = OUTPUT_MID; // 前进后退停止

        sbus_tx.data(sbus_tx_data);
        sbus_tx.Write();
        digitalWrite(pin_definition.led_pin, LOW);
    }

    delay(1);
}
